******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 00:48:20 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000022e9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00003100  00004f00  R  X
  SRAM                  20200000   00004000  00000400  00003c00  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003100   00003100    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002830   00002830    r-x .text
  000028f0    000028f0    000007d8   000007d8    r-- .rodata
  000030c8    000030c8    00000038   00000038    r-- .cinit
20200000    20200000    00000203   00000000    rw-
  20200000    20200000    000001ed   00000000    rw- .bss
  202001f0    202001f0    00000013   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002830     
                  000000c0    00000288     tft180.o (.text.tft180_init)
                  00000348    0000020c     encoder.o (.text.encoder_exti_callback)
                  00000554    00000204     openmv.o (.text.openmv_display_data)
                  00000758    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000944    000001bc     tft180.o (.text.func_float_to_str)
                  00000b00    00000130     tft180.o (.text.tft180_show_char_color)
                  00000c30    0000012c     openmv.o (.text.UART3_IRQHandler)
                  00000d5c    0000010c     tft180.o (.text.tft180_show_num_color)
                  00000e68    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000f6c    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001054    000000dc     empty.o (.text.main)
                  00001130    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001208    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000012dc    000000bc     servo.o (.text.servo_init)
                  00001398    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  00001424    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  000014b0    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000153c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000015c0    00000080     servo.o (.text.servo_config_type)
                  00001640    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000016bc    00000078     openmv.o (.text.openmv_init)
                  00001734    00000078     tft180.o (.text.tft180_clear_color)
                  000017ac    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001820    00000074     delay.o (.text.delay_us)
                  00001894    0000006c     tft180.o (.text.tft180_set_region)
                  00001900    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001968    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000019d0    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001a32    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001a34    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00001a96    00000062     tft180.o (.text.tft180_show_string_color)
                  00001af8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00001b4e    00000002     empty.o (.text.timerA_callback)
                  00001b50    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00001ba4    00000050     openmv.o (.text.openmv_is_data_valid)
                  00001bf4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001c40    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001c88    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001cd0    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  00001d18    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00001d5c    00000044     tft180.o (.text.tft180_write_16bit_data)
                  00001da0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  00001de0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  00001e20    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  00001e60    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001ea0    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001edc    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  00001f18    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001f54    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001f90    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001fcc    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00002006    00000002     empty.o (.text.timerB_callback)
                  00002008    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00002042    00000002     --HOLE-- [fill = 0]
                  00002044    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  0000207c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000020b0    00000034     openmv.o (.text.openmv_analysis)
                  000020e4    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00002114    00000030     tft180.o (.text.tft180_write_index)
                  00002144    0000002c     openmv.o (.text.__NVIC_ClearPendingIRQ)
                  00002170    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  0000219c    0000002c     openmv.o (.text.__NVIC_EnableIRQ)
                  000021c8    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  000021f4    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  00002220    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002248    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  00002270    00000028     timer.o (.text.TIMG8_IRQHandler)
                  00002298    00000028     debug.o (.text.UART0_IRQHandler)
                  000022c0    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000022e8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002310    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  00002334    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002356    00000002     --HOLE-- [fill = 0]
                  00002358    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002378    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002396    00000002     --HOLE-- [fill = 0]
                  00002398    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000023b4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000023d0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000023ec    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002408    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00002424    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002440    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  0000245c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002478    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002494    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000024b0    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  000024cc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000024e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000024fc    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002514    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000252c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002544    00000018     tft180.o (.text.DL_GPIO_setPins)
                  0000255c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00002574    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  0000258c    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  000025a4    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  000025bc    00000018     tft180.o (.text.DL_SPI_isBusy)
                  000025d4    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  000025ec    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002604    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0000261c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002634    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  0000264c    00000018     servo.o (.text.DL_Timer_startCounter)
                  00002664    00000018     servo.o (.text.DL_Timer_stopCounter)
                  0000267c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002694    00000018     openmv.o (.text.DL_UART_isRXFIFOEmpty)
                  000026ac    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000026c4    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000026da    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  000026f0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002706    00000016     delay.o (.text.delay_ms)
                  0000271c    00000016     timer.o (.text.timerA_init)
                  00002732    00000016     timer.o (.text.timerB_init)
                  00002748    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000275e    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  00002772    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002786    00000002     --HOLE-- [fill = 0]
                  00002788    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  0000279c    00000014     servo.o (.text.DL_Timer_enableClock)
                  000027b0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000027c4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000027d8    00000014     debug.o (.text.DL_UART_receiveData)
                  000027ec    00000014     openmv.o (.text.DL_UART_receiveData)
                  00002800    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00002812    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  00002824    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  00002836    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  00002848    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000285a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000286c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000287e    00000002     --HOLE-- [fill = 0]
                  00002880    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002890    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000028a0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000028b0    0000000c     timer.o (.text.get_system_time_ms)
                  000028bc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000028c6    00000008     empty.o (.text.GROUP1_IRQHandler)
                  000028ce    00000002     --HOLE-- [fill = 0]
                  000028d0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000028d8    00000006     libc.a : exit.c.obj (.text:abort)
                  000028de    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000028e2    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000028e6    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000028ea    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000028ee    00000002     --HOLE-- [fill = 0]

.cinit     0    000030c8    00000038     
                  000030c8    0000000f     (.cinit..data.load) [load image, compression = lzss]
                  000030d7    00000001     --HOLE-- [fill = 0]
                  000030d8    0000000c     (__TI_handler_table)
                  000030e4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000030ec    00000010     (__TI_cinit_table)
                  000030fc    00000004     --HOLE-- [fill = 0]

.rodata    0    000028f0    000007d8     
                  000028f0    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  00002ee0    00000016     empty.o (.rodata.str1.4000995719088696555.1)
                  00002ef6    00000015     empty.o (.rodata.str1.15930989295766594416.1)
                  00002f0b    00000015     empty.o (.rodata.str1.5100843677217179155.1)
                  00002f20    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  00002f34    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  00002f48    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00002f5c    00000013     openmv.o (.rodata.str1.14055760531511630791.1)
                  00002f6f    00000013     empty.o (.rodata.str1.7079713434352825882.1)
                  00002f82    00000012     openmv.o (.rodata.str1.10222307361326560281.1)
                  00002f94    00000012     openmv.o (.rodata.str1.10322375466862398049.1)
                  00002fa6    00000012     openmv.o (.rodata.str1.10499335994202400488.1)
                  00002fb8    00000012     openmv.o (.rodata.str1.11972700756869316087.1)
                  00002fca    00000012     openmv.o (.rodata.str1.12960560650680968270.1)
                  00002fdc    00000012     openmv.o (.rodata.str1.12983843792890534433.1)
                  00002fee    00000012     openmv.o (.rodata.str1.16410698957387474858.1)
                  00003000    00000012     openmv.o (.rodata.str1.4018680016288177859.1)
                  00003012    00000012     openmv.o (.rodata.str1.5573925365973559781.1)
                  00003024    00000012     openmv.o (.rodata.str1.8871796710599046883.1)
                  00003036    00000012     openmv.o (.rodata.str1.9558640640855864504.1)
                  00003048    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  00003052    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  0000305c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00003066    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00003070    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  0000307a    0000000a     openmv.o (.rodata.str1.16395811435273266920.1)
                  00003084    0000000a     openmv.o (.rodata.str1.9416414711272993270.1)
                  0000308e    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  00003090    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  00003098    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  000030a0    00000008     servo.o (.rodata.servo_init.servo_pwm_config)
                  000030a8    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  000030ab    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  000030ae    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  000030b1    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  000030b4    00000003     servo.o (.rodata.servo_init.servo_clock_config)
                  000030b7    00000003     openmv.o (.rodata.str1.15289475315984735280.1)
                  000030ba    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  000030bc    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000030be    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  000030c0    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  000030c2    00000006     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001ed     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    00000018     servo.o (.bss.servo_configs)
                  202001d8    0000000c     (.common:openmvData)
                  202001e4    00000008     openmv.o (.bss.rx_buffer)
                  202001ec    00000001     openmv.o (.bss.data)

.data      0    202001f0    00000013     UNINITIALIZED
                  202001f0    00000004     timer.o (.data.system_time_ms)
                  202001f4    00000002     encoder.o (.data.left_counter)
                  202001f6    00000002     encoder.o (.data.right_counter)
                  202001f8    00000002     openmv.o (.data.tft180_bgcolor)
                  202001fa    00000002     tft180.o (.data.tft180_bgcolor)
                  202001fc    00000002     openmv.o (.data.tft180_pencolor)
                  202001fe    00000001     openmv.o (.data.n)
                  202001ff    00000001     openmv.o (.data.state)
                  20200200    00000001     tft180.o (.data.tft180_x_max)
                  20200201    00000001     tft180.o (.data.tft180_y_max)
                  20200202    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2814    128       448    
       empty.o                        232     103       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3054    423       448    
                                                               
    .\drivers\
       tft180.o                       2240    1520      4      
       openmv.o                       1218    240       27     
       encoder.o                      598     0         4      
       servo.o                        384     11        24     
       timer.o                        230     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         4670    1771      63     
                                                               
    .\soft\
       delay.o                        138     0         0      
       debug.o                        78      0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         216     0         1      
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         774     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         292     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatunsisf.S.obj              40      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1264    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       51        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   10274   2245      1024   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000030ec records: 2, size/record: 8, table size: 16
	.data: load addr=000030c8, load size=0000000f bytes, run addr=202001f0, run size=00000013 bytes, compression=lzss
	.bss: load addr=000030e4, load size=00000008 bytes, run addr=20200000, run size=000001ed bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000030d8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
000028df  ADC0_IRQHandler                 
000028df  ADC1_IRQHandler                 
000028df  AES_IRQHandler                  
000028e2  C$$EXIT                         
000028df  CANFD0_IRQHandler               
000028df  DAC0_IRQHandler                 
000028bd  DL_Common_delayCycles           
00001d19  DL_SPI_init                     
00002801  DL_SPI_setClockConfig           
00000e69  DL_Timer_initFourCCPWMMode      
00000f6d  DL_Timer_initTimerMode          
0000245d  DL_Timer_setCaptCompUpdateMethod
00002635  DL_Timer_setCaptureCompareOutCtl
00002891  DL_Timer_setCaptureCompareValue 
00002479  DL_Timer_setClockConfig         
00001c41  DL_UART_init                    
00002849  DL_UART_setClockConfig          
000028df  DMA_IRQHandler                  
000028df  Default_Handler                 
000028df  GROUP0_IRQHandler               
000028c7  GROUP1_IRQHandler               
000028e3  HOSTexit                        
000028df  HardFault_Handler               
000028df  I2C0_IRQHandler                 
000028df  I2C1_IRQHandler                 
000028df  NMI_Handler                     
000028df  PendSV_Handler                  
000028df  RTC_IRQHandler                  
000028e7  Reset_Handler                   
000028df  SPI0_IRQHandler                 
000028df  SPI1_IRQHandler                 
000028df  SVC_Handler                     
00000759  SYSCFG_DL_GPIO_init             
00001399  SYSCFG_DL_PWM_6_init            
00001425  SYSCFG_DL_PWM_7_init            
00001da1  SYSCFG_DL_SPI_IMU660RB_init     
00002335  SYSCFG_DL_SYSCTL_init           
000028a1  SYSCFG_DL_SYSTICK_init          
00001de1  SYSCFG_DL_TFT_SPI_init          
00001edd  SYSCFG_DL_TIMER_12_init         
00001e21  SYSCFG_DL_TIMER_8_init          
00001c89  SYSCFG_DL_UART_0_init           
00001b51  SYSCFG_DL_UART_1_init           
00001cd1  SYSCFG_DL_UART_3_init           
00001901  SYSCFG_DL_init                  
00001209  SYSCFG_DL_initPower             
000028df  SysTick_Handler                 
000028df  TIMA0_IRQHandler                
000028df  TIMA1_IRQHandler                
000028df  TIMG0_IRQHandler                
000024b1  TIMG12_IRQHandler               
000028df  TIMG6_IRQHandler                
000028df  TIMG7_IRQHandler                
00002271  TIMG8_IRQHandler                
0000285b  TI_memcpy_small                 
00002299  UART0_IRQHandler                
000028df  UART1_IRQHandler                
000028df  UART2_IRQHandler                
00000c31  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
000030ec  __TI_CINIT_Base                 
000030fc  __TI_CINIT_Limit                
000030fc  __TI_CINIT_Warm                 
000030d8  __TI_Handler_Table_Base         
000030e4  __TI_Handler_Table_Limit        
00001f91  __TI_auto_init_nobinit_nopinit  
00001641  __TI_decompress_lzss            
0000286d  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00002749  __TI_zero_init_nomemset         
0000113b  __addsf3                        
000019d1  __aeabi_dcmpeq                  
00001a0d  __aeabi_dcmpge                  
00001a21  __aeabi_dcmpgt                  
000019f9  __aeabi_dcmple                  
000019e5  __aeabi_dcmplt                  
00001e61  __aeabi_f2d                     
00002045  __aeabi_f2iz                    
0000113b  __aeabi_fadd                    
00001a35  __aeabi_fcmpeq                  
00001a71  __aeabi_fcmpge                  
00001a85  __aeabi_fcmpgt                  
00001a5d  __aeabi_fcmple                  
00001a49  __aeabi_fcmplt                  
000014b1  __aeabi_fmul                    
00001131  __aeabi_fsub                    
00001f19  __aeabi_i2f                     
00001af9  __aeabi_idiv                    
00001a33  __aeabi_idiv0                   
00001af9  __aeabi_idivmod                 
000028d1  __aeabi_memcpy                  
000028d1  __aeabi_memcpy4                 
000028d1  __aeabi_memcpy8                 
000022c1  __aeabi_ui2f                    
ffffffff  __binit__                       
00001969  __cmpdf2                        
00001fcd  __cmpsf2                        
00001969  __eqdf2                         
00001fcd  __eqsf2                         
00001e61  __extendsfdf2                   
00002045  __fixsfsi                       
00001f19  __floatsisf                     
000022c1  __floatunsisf                   
000017ad  __gedf2                         
00001f55  __gesf2                         
000017ad  __gtdf2                         
00001f55  __gtsf2                         
00001969  __ledf2                         
00001fcd  __lesf2                         
00001969  __ltdf2                         
00001fcd  __ltsf2                         
UNDEFED   __mpu_init                      
00002009  __muldsi3                       
000014b1  __mulsf3                        
00001969  __nedf2                         
00001fcd  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00001131  __subsf3                        
000022e9  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000028eb  _system_pre_init                
000028d9  abort                           
000028f0  ascii_font_8x16                 
ffffffff  binit                           
00002707  delay_ms                        
00001821  delay_us                        
00000349  encoder_exti_callback           
00000945  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
000028b1  get_system_time_ms              
00000000  interruptVectors                
202001f4  left_counter                    
00001055  main                            
202001d8  openmvData                      
000020b1  openmv_analysis                 
00000555  openmv_display_data             
000016bd  openmv_init                     
00001ba5  openmv_is_data_valid            
202001f6  right_counter                   
000015c1  servo_config_type               
000012dd  servo_init                      
00001735  tft180_clear_color              
000000c1  tft180_init                     
00000b01  tft180_show_char_color          
00000d5d  tft180_show_num_color           
00001a97  tft180_show_string_color        
00001d5d  tft180_write_16bit_data         
000021f5  tft180_write_8bit_data          
00001b4f  timerA_callback                 
0000271d  timerA_init                     
00002007  timerB_callback                 
00002733  timerB_init                     
20200202  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  tft180_init                     
00000200  __STACK_SIZE                    
00000349  encoder_exti_callback           
00000555  openmv_display_data             
00000759  SYSCFG_DL_GPIO_init             
00000945  func_float_to_str               
00000b01  tft180_show_char_color          
00000c31  UART3_IRQHandler                
00000d5d  tft180_show_num_color           
00000e69  DL_Timer_initFourCCPWMMode      
00000f6d  DL_Timer_initTimerMode          
00001055  main                            
00001131  __aeabi_fsub                    
00001131  __subsf3                        
0000113b  __addsf3                        
0000113b  __aeabi_fadd                    
00001209  SYSCFG_DL_initPower             
000012dd  servo_init                      
00001399  SYSCFG_DL_PWM_6_init            
00001425  SYSCFG_DL_PWM_7_init            
000014b1  __aeabi_fmul                    
000014b1  __mulsf3                        
000015c1  servo_config_type               
00001641  __TI_decompress_lzss            
000016bd  openmv_init                     
00001735  tft180_clear_color              
000017ad  __gedf2                         
000017ad  __gtdf2                         
00001821  delay_us                        
00001901  SYSCFG_DL_init                  
00001969  __cmpdf2                        
00001969  __eqdf2                         
00001969  __ledf2                         
00001969  __ltdf2                         
00001969  __nedf2                         
000019d1  __aeabi_dcmpeq                  
000019e5  __aeabi_dcmplt                  
000019f9  __aeabi_dcmple                  
00001a0d  __aeabi_dcmpge                  
00001a21  __aeabi_dcmpgt                  
00001a33  __aeabi_idiv0                   
00001a35  __aeabi_fcmpeq                  
00001a49  __aeabi_fcmplt                  
00001a5d  __aeabi_fcmple                  
00001a71  __aeabi_fcmpge                  
00001a85  __aeabi_fcmpgt                  
00001a97  tft180_show_string_color        
00001af9  __aeabi_idiv                    
00001af9  __aeabi_idivmod                 
00001b4f  timerA_callback                 
00001b51  SYSCFG_DL_UART_1_init           
00001ba5  openmv_is_data_valid            
00001c41  DL_UART_init                    
00001c89  SYSCFG_DL_UART_0_init           
00001cd1  SYSCFG_DL_UART_3_init           
00001d19  DL_SPI_init                     
00001d5d  tft180_write_16bit_data         
00001da1  SYSCFG_DL_SPI_IMU660RB_init     
00001de1  SYSCFG_DL_TFT_SPI_init          
00001e21  SYSCFG_DL_TIMER_8_init          
00001e61  __aeabi_f2d                     
00001e61  __extendsfdf2                   
00001edd  SYSCFG_DL_TIMER_12_init         
00001f19  __aeabi_i2f                     
00001f19  __floatsisf                     
00001f55  __gesf2                         
00001f55  __gtsf2                         
00001f91  __TI_auto_init_nobinit_nopinit  
00001fcd  __cmpsf2                        
00001fcd  __eqsf2                         
00001fcd  __lesf2                         
00001fcd  __ltsf2                         
00001fcd  __nesf2                         
00002007  timerB_callback                 
00002009  __muldsi3                       
00002045  __aeabi_f2iz                    
00002045  __fixsfsi                       
000020b1  openmv_analysis                 
000021f5  tft180_write_8bit_data          
00002271  TIMG8_IRQHandler                
00002299  UART0_IRQHandler                
000022c1  __aeabi_ui2f                    
000022c1  __floatunsisf                   
000022e9  _c_int00_noargs                 
00002335  SYSCFG_DL_SYSCTL_init           
0000245d  DL_Timer_setCaptCompUpdateMethod
00002479  DL_Timer_setClockConfig         
000024b1  TIMG12_IRQHandler               
00002635  DL_Timer_setCaptureCompareOutCtl
00002707  delay_ms                        
0000271d  timerA_init                     
00002733  timerB_init                     
00002749  __TI_zero_init_nomemset         
00002801  DL_SPI_setClockConfig           
00002849  DL_UART_setClockConfig          
0000285b  TI_memcpy_small                 
0000286d  __TI_decompress_none            
00002891  DL_Timer_setCaptureCompareValue 
000028a1  SYSCFG_DL_SYSTICK_init          
000028b1  get_system_time_ms              
000028bd  DL_Common_delayCycles           
000028c7  GROUP1_IRQHandler               
000028d1  __aeabi_memcpy                  
000028d1  __aeabi_memcpy4                 
000028d1  __aeabi_memcpy8                 
000028d9  abort                           
000028df  ADC0_IRQHandler                 
000028df  ADC1_IRQHandler                 
000028df  AES_IRQHandler                  
000028df  CANFD0_IRQHandler               
000028df  DAC0_IRQHandler                 
000028df  DMA_IRQHandler                  
000028df  Default_Handler                 
000028df  GROUP0_IRQHandler               
000028df  HardFault_Handler               
000028df  I2C0_IRQHandler                 
000028df  I2C1_IRQHandler                 
000028df  NMI_Handler                     
000028df  PendSV_Handler                  
000028df  RTC_IRQHandler                  
000028df  SPI0_IRQHandler                 
000028df  SPI1_IRQHandler                 
000028df  SVC_Handler                     
000028df  SysTick_Handler                 
000028df  TIMA0_IRQHandler                
000028df  TIMA1_IRQHandler                
000028df  TIMG0_IRQHandler                
000028df  TIMG6_IRQHandler                
000028df  TIMG7_IRQHandler                
000028df  UART1_IRQHandler                
000028df  UART2_IRQHandler                
000028e2  C$$EXIT                         
000028e3  HOSTexit                        
000028e7  Reset_Handler                   
000028eb  _system_pre_init                
000028f0  ascii_font_8x16                 
000030d8  __TI_Handler_Table_Base         
000030e4  __TI_Handler_Table_Limit        
000030ec  __TI_CINIT_Base                 
000030fc  __TI_CINIT_Limit                
000030fc  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001d8  openmvData                      
202001f4  left_counter                    
202001f6  right_counter                   
20200202  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[174 symbols]
